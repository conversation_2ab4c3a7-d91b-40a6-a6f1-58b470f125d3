<?php
require_once '../db.php';
$pdo = getPDO();

try {
    $stmt = $pdo->prepare("
        SELECT
            A.id, A.dateAccident, A.typeAccident, A.causeAccident,
            <PERSON>.remar<PERSON>cci<PERSON>,
            E.nomEquipement,
            S2.nomSecteur as secteurEquipement,
            S3.nomSecteur as secteurAccident,
            AP.personneLibre, AP.typeContrat, AP.partieBlessée, AP.joursArrets,
            P.preno<PERSON>, P.nomPerso,
            S1.nomSecteur as secteurPersonne,
            COALESCE(PC.nombrePersonnes, 0) as nombrePersonnes
        FROM accident A
        LEFT JOIN equipement E ON E.id = A.idEquipement
        LEFT JOIN secteur S2 ON S2.id = E.idSecteur
        LEFT JOIN secteur S3 ON S3.id = A.secteurAccident
        LEFT JOIN (
            SELECT DISTINCT
                idAccident,
                FIRST_VALUE(idPersonne) OVER (PARTITION BY idAccident ORDER BY id) as id<PERSON>erson<PERSON>,
                FIRST_VALUE(personneLibre) OVER (PARTITION BY idAccident ORDER BY id) as personneLibre,
                FIRST_VALUE(typeContrat) OVER (PARTITION BY idAccident ORDER BY id) as typeContrat,
                FIRST_VALUE(partieBlessée) OVER (PARTITION BY idAccident ORDER BY id) as partieBlessée,
                FIRST_VALUE(joursArrets) OVER (PARTITION BY idAccident ORDER BY id) as joursArrets
            FROM accident_personne
        ) AP ON AP.idAccident = A.id
        LEFT JOIN personne P ON P.id = AP.idPersonne
        LEFT JOIN secteur S1 ON S1.id = P.idSecteur
        LEFT JOIN (
            SELECT idAccident, COUNT(*) as nombrePersonnes
            FROM accident_personne
            GROUP BY idAccident
        ) PC ON PC.idAccident = A.id
        ORDER BY A.dateAccident DESC
    ");

    $stmt->execute();
    $accidents = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Convertir les valeurs numériques appropriées
    foreach ($accidents as &$accident) {
        $accident['id'] = (int) $accident['id'];
        $accident['joursArrets'] = (int) $accident['joursArrets'];
        $accident['nombrePersonnes'] = (int) $accident['nombrePersonnes'];
    }

    header('Content-Type: application/json');
    echo json_encode($accidents);

} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['error' => $e->getMessage()]);
}
?>
