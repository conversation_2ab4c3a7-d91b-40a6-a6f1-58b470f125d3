<?php
ob_start();
?>

<div class="py-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">
        <i class="fas fa-chart-bar text-primary-600 mr-3"></i>
        Tableau de bord analytique
      </h1>
      <p class="text-gray-600 mt-1">Analyse des données d'accidents et visualisations</p>
    </div>
    <div class="flex space-x-3">
      <a href="index.php" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
        <i class="fas fa-arrow-left mr-2"></i>
        Retour aux accidents
      </a>
      <button id="refreshChartsBtn" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors">
        <i class="fas fa-sync-alt mr-2"></i>
        Actualiser
      </button>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
      <!-- Date de début -->
      <div>
        <label for="analyticsStartDate" class="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
        <input type="date" id="analyticsStartDate" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
      </div>

      <!-- Date de fin -->
      <div>
        <label for="analyticsEndDate" class="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
        <input type="date" id="analyticsEndDate" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
      </div>

      <!-- Secteur -->
      <div>
        <label for="analyticsSector" class="block text-sm font-medium text-gray-700 mb-1">Secteur</label>
        <select id="analyticsSector" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
          <option value="">Tous les secteurs</option>
        </select>
      </div>

      <!-- Type d'accident -->
      <div>
        <label for="analyticsType" class="block text-sm font-medium text-gray-700 mb-1">Type d'accident</label>
        <select id="analyticsType" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
          <option value="">Tous les types</option>
        </select>
      </div>
    </div>

    <div class="flex justify-end mt-4 space-x-3">
      <button id="resetAnalyticsFilters" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
        <i class="fas fa-undo mr-1"></i> Réinitialiser
      </button>
      <button id="applyAnalyticsFilters" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
        <i class="fas fa-search mr-1"></i> Appliquer les filtres
      </button>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow" onclick="redirectToAccidents('accidents')">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
            <i class="fas fa-exclamation-triangle text-red-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Accidents</p>
          <p class="text-2xl font-semibold text-gray-900" id="totalAccidents">-</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow" onclick="redirectToAccidents('premiers-soins')">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
            <i class="fas fa-first-aid text-blue-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Premiers Soins</p>
          <p class="text-2xl font-semibold text-gray-900" id="totalPremiersSoins">-</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow" onclick="redirectToAccidents('presque-accidents')">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
            <i class="fas fa-exclamation text-yellow-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Presques Accidents</p>
          <p class="text-2xl font-semibold text-gray-900" id="totalPresqueAccidents">-</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow" onclick="redirectToAccidents('situations-dangereuses')">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
            <i class="fas fa-shield-alt text-green-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Situations dangereuses</p>
          <p class="text-2xl font-semibold text-gray-900" id="totalSituationsDangereuses">-</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
    <!-- Chart 1: Accidents by Sector -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6">
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900 mb-2 sm:mb-0">
          <i class="fas fa-chart-bar text-green-500 mr-2"></i>
          Accidents par secteur
        </h3>
        <div class="text-sm text-gray-500">
          <i class="fas fa-info-circle"></i>
          Répartition par type
        </div>
      </div>
      <div class="relative" style="height: 350px;">
        <canvas id="sectorChart"></canvas>
      </div>
    </div>

    <!-- Chart 2: Monthly Distribution -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6">
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900 mb-2 sm:mb-0">
          <i class="fas fa-chart-line text-purple-500 mr-2"></i>
          Distribution mensuelle
        </h3>
        <div class="text-sm text-gray-500">
          <i class="fas fa-info-circle"></i>
          Évolution dans l'année
        </div>
      </div>
      <div class="relative" style="height: 350px;">
        <canvas id="monthlyChart"></canvas>
      </div>
    </div>
  </div>


  <!-- Loading indicator -->
  <div id="analyticsLoading" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 shadow-xl">
      <div class="flex items-center space-x-3">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="text-gray-700">Chargement des données analytiques...</span>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="js/analytics.js"></script>

<?php
$content = ob_get_clean();
$title = "Tableau de bord analytique - Accidents";
require 'template.php';
?>
