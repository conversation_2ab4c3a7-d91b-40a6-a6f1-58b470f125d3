// Fonction helper pour convertir les IDs de type en libellés
function getTypeLabel(typeId) {
    switch (parseInt(typeId)) {
        case 1: return 'Accident';
        case 4: return 'Accident de trajet';
        case 5: return 'Premier soins';
        case 2: return 'Presque accident';
        case 3: return 'Situation dangereuse';
        default: return 'Non défini';
    }
}

// Fonction pour auto-remplir le secteur depuis l'équipement sélectionné
function autoFillSecteurFromEquipement(equipementId, secteurSelectId) {
    const secteurSelect = document.getElementById(secteurSelectId);
    if (!secteurSelect || secteurSelect.value !== '') {
        // Ne pas écraser si un secteur est déjà sélectionné
        return;
    }

    // Trouver l'équipement dans les données
    const equipement = equipementsData.find(e => e.id == equipementId);
    if (!equipement || !equipement.secteurEquipement) {
        return;
    }

    // Trouver le secteur correspondant dans les données de secteurs
    const secteur = secteursData.find(s => s.nomSecteur === equipement.secteurEquipement);
    if (secteur) {
        secteurSelect.value = secteur.id;
    }
}

// Variables globales pour stocker les données
let personnesData = [];
let equipementsData = [];
let secteursData = [];
let allAccidentsData = []; // Stocker tous les accidents pour le filtrage côté client

// Fonction utilitaire pour obtenir les informations de type d'accident
function getTypeInfo(typeAccident) {
    const typeId = parseInt(typeAccident);

    switch (typeId) {
        case 1: // Accident
            return {
                class: 'bg-red-100 text-red-800',
                label: 'Accident'
            };
        case 4: // Accident de trajet
            return {
                class: 'bg-red-100 text-red-800',
                label: 'Accident de trajet'
            };
        case 5: // Premier soins
            return {
                class: 'bg-blue-100 text-blue-800',
                label: 'Premier soins'
            };
        case 2: // Presque accident
            return {
                class: 'bg-yellow-100 text-yellow-800',
                label: 'Presque accident'
            };
        case 3: // Situation dangereuse
            return {
                class: 'bg-green-100 text-green-800',
                label: 'Situation dangereuse'
            };
        default:
            return {
                class: 'bg-gray-100 text-gray-800',
                label: 'Non défini'
            };
    }
}

// Fonction utilitaire pour obtenir l'affichage du secteur
function getSecteurDisplay(accident) {
    let secteurHTML = '';

    // Priorité au secteur de l'accident, sinon secteur de l'équipement
    if (accident.secteurAccident) {
        secteurHTML = `<div class="text-xs text-blue-600">Secteur: ${accident.secteurAccident}</div>`;
    } else if (accident.secteurEquipement) {
        secteurHTML = `<div class="text-xs text-gray-500">Secteur: ${accident.secteurEquipement}</div>`;
    }

    return secteurHTML;
}

// Fonction utilitaire pour obtenir seulement le label du type (pour les détails)
function getTypeLabel(typeAccident) {
    return getTypeInfo(typeAccident).label;
}

// Fonction pour obtenir les accidents actuellement affichés (après filtrage)
function getCurrentlyDisplayedAccidents() {
    // Cette fonction sera utilisée pour l'export Excel
    // Elle retourne les données filtrées actuellement affichées
    const tableRows = document.querySelectorAll('#accident-container tbody tr');
    const displayedAccidents = [];

    console.log(`🔍 getCurrentlyDisplayedAccidents: Found ${tableRows.length} rows`);

    tableRows.forEach(row => {
        const accidentId = row.getAttribute('data-accident-id');
        console.log(`🔍 Row accident ID: ${accidentId}`);
        if (accidentId && allAccidentsData.length > 0) {
            const accident = allAccidentsData.find(a => a.id == accidentId);
            if (accident) {
                displayedAccidents.push(accident);
            }
        }
    });

    console.log(`🔍 getCurrentlyDisplayedAccidents: Returning ${displayedAccidents.length} accidents`);
    return displayedAccidents;
}

// Fonction pour exporter vers Excel
function exportToExcel(data, filename) {
    // Créer un CSV (compatible Excel) avec délimiteur point-virgule pour locale française
    const csvContent = data.map(row =>
        row.map(cell => {
            // Échapper les guillemets et entourer de guillemets si nécessaire
            const cellStr = String(cell || '');
            if (cellStr.includes(';') || cellStr.includes('"') || cellStr.includes('\n')) {
                return '"' + cellStr.replace(/"/g, '""') + '"';
            }
            return cellStr;
        }).join(';') // Utiliser point-virgule comme délimiteur pour Excel français
    ).join('\n');

    // Ajouter BOM pour UTF-8 (pour Excel)
    const BOM = '\uFEFF';
    const csvWithBOM = BOM + csvContent;

    // Créer et télécharger le fichier
    const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Toast.fire({
            icon: 'success',
            title: 'Export Excel réussi'
        });
    } else {
        Toast.fire({
            icon: 'error',
            title: 'Export non supporté par ce navigateur'
        });
    }
}

// Fonction pour précharger toutes les données au démarrage
function preloadAllData() {

    // Afficher un indicateur de chargement
    const loadingToast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 15000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });

    loadingToast.fire({
        icon: 'info',
        title: 'Chargement des données...'
    });


    // Charger les personnes
    $.ajax({
        url: 'ajax/get_personnes.php',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            if (Array.isArray(data)) {
                personnesData = data;
                populateDatalist('personnes-options', personnesData, item => item.id, item => `${item.prenomPerso} ${item.nomPerso}`);
            } else {
                console.error('Les données personnes ne sont pas un tableau');
                generateFallbackData('personnes');
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des personnes:', textStatus, errorThrown);
            generateFallbackData('personnes');
        }
    });

    // Charger les équipements
    $.ajax({
        url: 'ajax/get_equipements.php',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            if (Array.isArray(data)) {
                equipementsData = data;
                populateDatalist('equipements-options', equipementsData, item => item.id, item => {
                    // Inclure le secteur s'il existe, séparé par un tiret
                    return item.secteurEquipement
                        ? `${item.nomEquipement} - ${item.secteurEquipement}`
                        : item.nomEquipement;
                });
            } else {
                console.error('Les données équipements ne sont pas un tableau');
                generateFallbackData('equipements');
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des équipements:', textStatus, errorThrown);
            generateFallbackData('equipements');
        }
    });

    // Charger les secteurs
    $.ajax({
        url: 'ajax/get_secteurs.php',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            if (Array.isArray(data)) {
                secteursData = data;
                populateSecteurSelects();
                loadingToast.close();
            } else {
                console.error('Les données secteurs ne sont pas un tableau');
                generateFallbackData('secteurs');
                loadingToast.close();
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des secteurs:', textStatus, errorThrown);
            generateFallbackData('secteurs');
            loadingToast.close();
        }
    });
}

// Fonction pour générer des données de secours
function generateFallbackData(type) {

    switch(type) {
        case 'personnes':
            personnesData = [];
            for (let i = 1; i <= 10; i++) {
                personnesData.push({
                    id: i,
                    prenomPerso: `Prénom${i}`,
                    nomPerso: `Nom${i}`
                });
            }
            populateDatalist('personnes-options', personnesData, item => item.id, item => `${item.prenomPerso} ${item.nomPerso}`);
            break;

        case 'equipements':
            equipementsData = [];
            for (let i = 1; i <= 10; i++) {
                equipementsData.push({
                    id: i,
                    nomEquipement: `Équipement ${i}`,
                    secteurEquipement: `Secteur ${Math.ceil(i/3)}`
                });
            }
            populateDatalist('equipements-options', equipementsData, item => item.id, item => {
                // Inclure le secteur séparé par un tiret
                return `${item.nomEquipement} - ${item.secteurEquipement}`;
            });
            break;

        case 'secteurs':
            secteursData = [];
            for (let i = 1; i <= 5; i++) {
                secteursData.push({
                    id: i,
                    nomSecteur: `Secteur ${i}`
                });
            }
            populateSecteurSelects();
            break;
    }
}

// Fonction pour remplir les selects de secteurs
function populateSecteurSelects() {
    const secteurSelects = ['secteurAccident', 'edit-secteurAccident'];

    secteurSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Vider le select
            select.innerHTML = '<option value="">Sélectionner...</option>';

            // Ajouter les options
            secteursData.forEach(secteur => {
                const option = document.createElement('option');
                option.value = secteur.id;
                option.textContent = secteur.nomSecteur;
                select.appendChild(option);
            });
        }
    });
}

// Fonction générique pour remplir une datalist
function populateDatalist(datalistId, data, valueGetter, labelGetter) {
    const datalist = document.getElementById(datalistId);
    if (!datalist) {
        console.error(`Datalist avec ID ${datalistId} non trouvée`);
        return;
    }


    // Vider la datalist
    datalist.innerHTML = '';

    // Ajouter les options
    data.forEach(item => {
        // Pour les personnes et équipements, on veut afficher le texte descriptif
        if (datalistId === 'personnes-options' || datalistId === 'equipements-options') {
            const label = labelGetter(item);  // Texte descriptif (nom ou équipement)
            const id = valueGetter(item);     // ID numérique

            // Créer l'option avec le texte comme valeur visible
            const optionHtml = `<option value="${label}" data-id="${id}">${label}</option>`;
            datalist.insertAdjacentHTML('beforeend', optionHtml);

            // Log pour débogage
            if (datalistId === 'personnes-options') {
            } else {
            }
        }
        else {
            const value = valueGetter(item);
            const label = labelGetter(item);

            // Créer l'option standard
            const optionHtml = `<option value="${value}">${label}</option>`;
            datalist.insertAdjacentHTML('beforeend', optionHtml);
        }
    });

    // Vérifier que les options ont été ajoutées correctement
}

$(document).ready(function () {
    // Configuration des notifications Toast
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        iconColor: '#0ea5e9',
        customClass: {
            popup: 'bg-white shadow-md rounded-lg'
        }
    });

    // Précharger toutes les données
    preloadAllData();

    // Initialiser les filtres simples
    initSimpleFilters();

    // Vérifier les datalists après un court délai pour s'assurer qu'elles sont chargées
    setTimeout(() => {

        // Vérifier la datalist des personnes
        const personnesDatalist = document.getElementById('personnes-options');
        if (personnesDatalist) {
            if (personnesDatalist.children.length > 0) {
                const firstOption = personnesDatalist.children[0];
            }
        } else {
            console.error("Datalist personnes-options non trouvée");
        }

        // Vérifier la datalist des équipements
        const equipementsDatalist = document.getElementById('equipements-options');
        if (equipementsDatalist) {
            if (equipementsDatalist.children.length > 0) {
                const firstOption = equipementsDatalist.children[0];
            }
        } else {
            console.error("Datalist equipements-options non trouvée");
        }
    }, 2000);

    // Initialisation - charger tous les accidents pour le filtrage côté client
    loadAllAccidentsData();
    enableTooltips();

    // Fonction pour initialiser les filtres simples (select normaux)
    function initSimpleFilters() {
        // Charger les types, années et secteurs dans les selects normaux
        loadSelectData('ajax/get_types.php', 'filterType', t => t.typeAccident);
        loadSelectData('ajax/get_annees.php', 'filterAnnee', a => a.annee);
        loadSelectData('ajax/get_secteurs.php', 'filterSecteur', s => s.nomSecteur);
        // set filterAnnee to current year
    }

    // Activer les tooltips avec Tippy.js (alternative à Bootstrap tooltips)
    function enableTooltips() {
        // Utiliser une approche compatible avec Tailwind sans dépendre de Bootstrap
        if (typeof tippy !== 'undefined') {
            // Si tippy.js est disponible
            tippy('[data-tippy-content]');
        } else {
            // Fallback simple si tippy n'est pas disponible
            $('[data-bs-toggle="tooltip"]').each(function() {
                const title = $(this).attr('title') || $(this).data('bs-title') || $(this).data('bs-original-title');
                if (title) {
                    $(this).attr('data-tippy-content', title);
                    $(this).removeAttr('data-bs-toggle');
                }
            });

            // Ajouter Tippy.js dynamiquement si nécessaire
            if ($('[data-tippy-content]').length > 0 && typeof tippy === 'undefined') {
                const tippyScript = document.createElement('script');
                tippyScript.src = 'https://unpkg.com/@popperjs/core@2';
                document.head.appendChild(tippyScript);

                const tippyMainScript = document.createElement('script');
                tippyMainScript.src = 'https://unpkg.com/tippy.js@6';
                tippyMainScript.onload = function() {
                    tippy('[data-tippy-content]');
                };
                document.head.appendChild(tippyMainScript);
            }
        }
    }

    // Charger tous les accidents au démarrage pour le filtrage côté client
    function loadAllAccidentsData() {

        $.ajax({
            url: 'ajax/get_accidents_data.php', // Nouveau endpoint pour récupérer les données JSON
            dataType: 'json',
            timeout: 10000,
            success: function(data) {
                if (Array.isArray(data)) {
                    allAccidentsData = data;

                    // set filterAnnee to current year
                    const currentYear = new Date().getFullYear();
                    $('#filterAnnee').val(currentYear);
                    applyFilters(); // Appliquer les filtres initiaux

                } else {
                    console.error('Les données accidents ne sont pas un tableau');
                    // Fallback vers l'ancien système
                    loadAccidentsOldWay();
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Erreur lors du chargement des accidents:', textStatus, errorThrown);
                // Fallback vers l'ancien système
                loadAccidentsOldWay();
            }
        });
    }

    // Fonction de fallback pour charger les accidents avec l'ancien système
    function loadAccidentsOldWay(queryParams = '') {
        $('#loading-indicator').removeClass('hidden');
        $('#accident-container').addClass('opacity-50');

        $.get('ajax/get_accidents.php' + queryParams, function (html) {
            $('#accident-container').html(html).removeClass('opacity-50');
            $('#loading-indicator').addClass('hidden');
            enableTooltips();
            attachPersonModalHandlers(); // Attacher les gestionnaires pour les modals
        }).fail(function() {
            $('#loading-indicator').addClass('hidden');
            $('#accident-container').removeClass('opacity-50');
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors du chargement des données'
            });
        });
    }

    // Fonction pour afficher les accidents filtrés côté client
    function displayFilteredAccidents(accidents) {
        $('#loading-indicator').removeClass('hidden');
        $('#accident-container').addClass('opacity-50');

        // Générer le HTML côté client
        let html = generateAccidentsHTML(accidents);

        $('#accident-container').html(html).removeClass('opacity-50');
        $('#loading-indicator').addClass('hidden');
        enableTooltips();
        attachPersonModalHandlers(); // Attacher les gestionnaires pour les modals
    }

    // Charger les accidents avec indicateur de chargement (fonction de compatibilité)
    function loadAccidents(queryParams = '') {
        if (allAccidentsData.length > 0) {
            // Utiliser le filtrage côté client si les données sont disponibles
            applyFilters();
        } else {
            // Fallback vers l'ancien système
            loadAccidentsOldWay(queryParams);
        }
    }

    // Fonction pour générer le HTML des accidents côté client
    function generateAccidentsHTML(accidents) {
        if (accidents.length === 0) {
            return `
                <div class="bg-white rounded-lg shadow-sm p-8 text-center border border-gray-200">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-clipboard-list text-gray-300 text-5xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">Aucun accident trouvé</h3>
                        <p class="text-gray-500">Aucun accident ne correspond à vos critères de recherche.</p>
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="overflow-x-auto">
                    <div class="max-h-[60vh] md:max-h-[65vh] overflow-y-auto" style="min-width: 1600px;" id="accident-table-container">
                        <table class="min-w-full divide-y divide-gray-200 table-fixed">
                            <thead class="bg-gray-50 sticky top-0 z-10">
                                <tr>
                                    <th scope="col" class="w-24 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                    <th scope="col" class="w-40 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Personne</th>
                                    <th scope="col" class="w-28 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Contrat</th>
                                    <th scope="col" class="w-40 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Équipement</th>
                                    <th scope="col" class="w-28 px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th scope="col" class="w-24 px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase">Partie blessée</th>
                                    <th scope="col" class="w-80 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cause</th>
                                    <th scope="col" class="w-80 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Remarques</th>
                                    <th scope="col" class="w-20 px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase">Jours d'arrêt</th>
                                    <th scope="col" class="w-24 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
        `;

        accidents.forEach(accident => {
            html += generateAccidentRowHTML(accident);
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Ajouter le modal pour les personnes
        html += `
            <!-- Modal pour afficher toutes les personnes d'un accident -->
            <div id="personnesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <!-- Header -->
                        <div class="flex justify-between items-center pb-3 border-b">
                            <h3 class="text-lg font-medium text-gray-900">
                                <i class="fas fa-users mr-2 text-blue-500"></i>
                                Personnes impliquées dans l'accident
                            </h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="closePersonnesModal()">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <!-- Contenu -->
                        <div class="mt-4">
                            <div id="personnesModalContent" class="space-y-4">
                                <!-- Le contenu sera chargé dynamiquement -->
                            </div>
                        </div>
                        <!-- Footer -->
                        <div class="flex justify-end pt-4 border-t mt-4">
                            <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="closePersonnesModal()">
                                Fermer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Ajouter le script pour mettre à jour le compteur
        html += `
            <script>
                // Update accident count
                document.getElementById('accident-count').textContent = '${accidents.length}';
            </script>
        `;

        return html;
    }

    // Fonction pour générer le HTML d'une ligne d'accident
    function generateAccidentRowHTML(a) {
        // Déterminer le nombre de personnes
        const nombrePersonnes = a.nombrePersonnes || 1;
        let personspace = "whitespace-nowrap";
        // Générer le HTML de la personne
        let personneHTML = '';
        if (nombrePersonnes === 1) {
            // Une seule personne
            if (a.prenomPerso && a.nomPerso) {
                personneHTML = `${a.prenomPerso} ${a.nomPerso}`;
            } else if (a.personneLibre) {
                personneHTML = a.personneLibre;
            } else {
                personneHTML = '';
            }
            if (personneHTML.split(' ').length >= 3) {
                personspace = "";
            }
        } else {
            // Plusieurs personnes - affichage condensé
            let firstPersonName = '';
            if (a.prenomPerso && a.nomPerso) {
                firstPersonName = `${a.prenomPerso} ${a.nomPerso}`;
            } else if (a.personneLibre) {
                firstPersonName = a.personneLibre;
            } else {
                firstPersonName = 'Personne 1';
            }
            if (firstPersonName.split(' ').length >= 3) {
                personspace = "";
            }

            personneHTML = `
                <div class="flex items-center space-x-2">
                    <div>${firstPersonName}</div>
                    <button
                        type="button"
                        class="show-all-personnes-btn inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors cursor-pointer"
                        data-accident-id="${a.id}"
                        title="Voir toutes les personnes impliquées"
                    >
                        +${nombrePersonnes - 1}
                    </button>
                </div>
            `;
        }

        // Déterminer les classes CSS et le label pour le type d'accident
        const typeInfo = getTypeInfo(a.typeAccident);
        const typeClass = typeInfo.class;
        const typeLabel = typeInfo.label;


        return `
            <tr class="hover:bg-gray-50 transition-colors" data-accident-id="${a.id}">
                <td class="px-3 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                        ${new Date(a.dateAccident).toLocaleDateString('fr-FR')}
                    </div>
                </td>
                <td class="px-3 py-4 ${personspace}">
                    <div class="text-sm text-gray-900 break-words">
                        ${personneHTML}
                    </div>
                </td>
                <td class="px-3 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                        ${a.typeContrat || '-'}
                    </div>
                </td>
                <td class="px-3 py-4">
                    <div class="text-sm text-gray-900 break-words">
                        ${a.nomEquipement || '-'}
                        ${getSecteurDisplay(a)}
                    </div>
                </td>
                <td class="px-3 py-4 whitespace-nowrap text-center">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${typeClass}">
                        ${typeLabel}
                    </span>
                </td>
                <td class="px-3 py-4 whitespace-nowrap text-center">
                    <div class="text-sm text-gray-900">
                        ${a.partieBlessée || '-'}
                    </div>
                </td>
                <td class="px-3 py-4">
                    <div class="text-sm text-gray-900 break-words">
                        ${a.causeAccident || '-'}
                    </div>
                </td>
                <td class="px-3 py-4">
                    <div class="text-sm text-gray-900 break-words">
                        ${a.remarquesAccident || '-'}
                    </div>
                </td>
                <td class="px-3 py-4 whitespace-nowrap text-center">
                    <div class="text-sm text-gray-900 font-medium">
                        ${a.joursArrets > 0 ? a.joursArrets : '-'}
                    </div>
                </td>
                <td class="px-3 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button
                            class="text-primary-600 hover:text-primary-900 edit-btn"
                            data-id="${a.id}"
                            data-bs-toggle="modal"
                            data-bs-target="#editAccidentModal"
                        >
                            <i class="fas fa-edit"></i>
                        </button>
                        <button
                            class="text-red-600 hover:text-red-900 delete-btn"
                            data-id="${a.id}"
                        >
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // Fonction pour attacher les gestionnaires d'événements pour les modals de personnes
    function attachPersonModalHandlers() {
        // Gestionnaire d'événements pour les boutons "+X"
        $(document).off('click', '.show-all-personnes-btn').on('click', '.show-all-personnes-btn', function(e) {
            e.preventDefault();
            const accidentId = $(this).data('accident-id');
            openPersonnesModal(accidentId);
        });

        // Fermer le modal en cliquant à l'extérieur
        $(document).off('click', '#personnesModal').on('click', '#personnesModal', function(e) {
            if (e.target === this) {
                closePersonnesModal();
            }
        });
    }

    // Fonctions pour gérer le modal des personnes
    window.openPersonnesModal = function(accidentId) {
        const modal = document.getElementById('personnesModal');
        const content = document.getElementById('personnesModalContent');

        // Afficher le modal
        modal.classList.remove('hidden');

        // Afficher un loader
        content.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i><p class="text-gray-500 mt-2">Chargement...</p></div>';

        // Charger les données
        fetch(`Ajax/get_accident_personnes.php?idAccident=${accidentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    content.innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Erreur lors du chargement</p></div>';
                    return;
                }

                if (data.length === 0) {
                    content.innerHTML = '<div class="text-center py-4 text-gray-500"><i class="fas fa-user-slash text-2xl"></i><p class="mt-2">Aucune personne impliquée</p></div>';
                    return;
                }

                // Générer le HTML pour chaque personne
                let html = '';
                data.forEach((personne, index) => {
                    html += generatePersonneCard(personne, index + 1);
                });

                content.innerHTML = html;
            })
            .catch(error => {
                console.error('Erreur:', error);
                content.innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Erreur lors du chargement</p></div>';
            });
    };

    window.closePersonnesModal = function() {
        const modal = document.getElementById('personnesModal');
        modal.classList.add('hidden');
    };

    // Fonction pour générer le HTML d'une carte personne
    function generatePersonneCard(personne, numero) {
        const nomComplet = personne.prenomPerso && personne.nomPerso
            ? `${personne.prenomPerso} ${personne.nomPerso}`
            : personne.personneLibre || '';

        const typePersonne = personne.prenomPerso && personne.nomPerso ? 'Salarié SCM' : 'Personne externe';

        return `
            <div class="bg-gray-50 p-4 rounded-lg border">
                <div class="flex items-start justify-between mb-3">
                    <h4 class="text-md font-medium text-gray-900">
                        <i class="fas fa-user mr-2 text-blue-500"></i>
                        Personne ${numero}
                    </h4>
                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        ${typePersonne}
                    </span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-medium text-gray-700">Nom</p>
                        <p class="text-sm text-gray-900">${nomComplet}</p>
                        ${personne.secteurPersonne ? `<p class="text-xs text-gray-500">Secteur: ${personne.secteurPersonne}</p>` : ''}
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-700">Type de contrat</p>
                        <p class="text-sm text-gray-900">${personne.typeContrat || '-'}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-700">Partie blessée</p>
                        <p class="text-sm text-gray-900">${personne.partieBlessée || '-'}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-700">Jours d'arrêt</p>
                        <p class="text-sm text-gray-900">
                            ${personne.joursArrets && personne.joursArrets > 0
                                ? `${personne.joursArrets} jour${personne.joursArrets > 1 ? 's' : ''}`
                                : '0 jour'}
                        </p>
                    </div>
                </div>
            </div>
        `;
    }

    // Charger options dans <select> (version simplifiée sans Tom Select)
    function loadSelectData(url, selectId, labelBuilder) {
        // Vérifier si l'élément existe
        const select = document.getElementById(selectId);
        if (!select) {
            console.warn(`L'élément avec l'ID ${selectId} n'existe pas.`);
            return;
        }

        // Ajouter une option de chargement
        const $select = $('#' + selectId);
        $select.html('<option value="">Chargement...</option>');

        // Charger les données
        $.ajax({
            url: url,
            dataType: 'json',
            timeout: 5000, // 5 secondes de timeout
            success: function(data) {
                try {
                    // Vérifier si les données sont valides
                    if (!Array.isArray(data)) {
                        throw new Error('Les données ne sont pas un tableau');
                    }

                    // Pour les selects normaux
                    $select.html('<option value="">Sélectionner...</option>');

                    // Ajouter les options
                    $.each(data, function(_, item) {
                        if (item && item.id !== undefined) {
                            const option = $('<option>').val(item.id).text(labelBuilder(item));
                            $select.append(option);
                        }
                    });

                    // si le select c'est le select Annee, on sélectionne l'année actuelle
                    if (selectId === 'filterAnnee') {
                        const currentYear = new Date().getFullYear();
                        $select.val(currentYear);
                    }
                } catch (error) {
                    console.warn('Erreur dans loadSelectData:', error);

                    // Fallback simple en cas d'erreur
                    $select.html('<option value="">Sélectionner...</option>');
                }
            },
            error: function(_, textStatus, errorThrown) {
                console.error('Erreur lors du chargement des données:', textStatus, errorThrown);

                // Fallback en cas d'erreur AJAX
                $select.html('<option value="">Sélectionner...</option>');
            }
        });
    }

    // Réinitialiser les filtres
    $('#resetFilters').on('click', function() {
        // Réinitialiser les selects normaux
        document.querySelectorAll('.filter-select').forEach(select => {
            select.value = '';
        });

        // Réinitialiser les inputs avec datalist (vérifier qu'ils existent)
        const filterPersonneElement = document.getElementById('filterPersonne');
        const filterEquipementElement = document.getElementById('filterEquipement');

        if (filterPersonneElement) {
            filterPersonneElement.value = '';
        }

        if (filterEquipementElement) {
            filterEquipementElement.value = '';
        }

        // Afficher tous les accidents (client-side ou recharger)
        if (allAccidentsData.length > 0) {
            displayFilteredAccidents(allAccidentsData);
        } else {
            loadAccidents();
        }
    });

    // Export Excel functionality
    $('#exportExcelBtn').on('click', function() {
        console.log('🔄 Export Excel clicked');

        // Get currently displayed data (filtered or all)
        // The table is dynamically generated, so we need to find tbody rows
        const tableRows = document.querySelectorAll('#accident-container tbody tr');

        console.log(`📊 Found ${tableRows.length} table rows`);
        console.log(`📊 allAccidentsData length: ${allAccidentsData.length}`);

        // Check if we have data to export
        if (tableRows.length === 0 && allAccidentsData.length === 0) {
            Toast.fire({
                icon: 'warning',
                title: 'Aucune donnée à exporter'
            });
            return;
        }

        // Prepare data for export
        const exportData = [];

        // Add header row
        exportData.push([
            'Date',
            'Personne',
            'Contrat',
            'Équipement',
            'Secteur',
            'Type',
            'Partie blessée',
            'Cause',
            'Remarques',
            'Jours d\'arrêt'
        ]);

        // Get data from currently displayed table or from allAccidentsData
        let dataToExport = [];

        if (allAccidentsData.length > 0) {
            // Use filtered data if available
            const currentlyDisplayed = getCurrentlyDisplayedAccidents();
            dataToExport = currentlyDisplayed.length > 0 ? currentlyDisplayed : allAccidentsData;
        } else {
            // Fallback: extract data from table rows
            console.log('📊 Using fallback method to extract data from table rows');
            tableRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                console.log(`📊 Row has ${cells.length} cells`);
                if (cells.length >= 10) { // Updated to 10 cells due to new "Remarques" column
                    dataToExport.push({
                        dateAccident: cells[0].textContent.trim(),
                        personne: cells[1].textContent.trim(),
                        typeContrat: cells[2].textContent.trim(),
                        equipement: cells[3].textContent.trim(),
                        type: cells[4].textContent.trim(),
                        partieBlessée: cells[5].textContent.trim(),
                        causeAccident: cells[6].textContent.trim(),
                        remarquesAccident: cells[7].textContent.trim(),
                        joursArrets: cells[8].textContent.trim()
                    });
                }
            });
        }

        console.log(`📊 Converting ${dataToExport.length} accidents to export format`);

        // Convert data to export format
        dataToExport.forEach(accident => {
            let personneText, equipementText, secteurText, typeText, partieBlesseeText, dateText;

            // Handle different data formats (from API vs from table extraction)
            if (accident.prenomPerso !== undefined || accident.nomPerso !== undefined) {
                // Data from API
                personneText = accident.prenomPerso && accident.nomPerso
                    ? `${accident.prenomPerso} ${accident.nomPerso}`
                    : accident.personneLibre || '-';
                equipementText = accident.nomEquipement || '-';
                secteurText = accident.secteurAccident || accident.secteurEquipement || '-';
                typeText = getTypeLabel(accident.typeAccident);
                partieBlesseeText = accident.partieBlessée || '-';
                dateText = new Date(accident.dateAccident).toLocaleDateString('fr-FR');
            } else {
                // Data from table extraction (fallback)
                personneText = accident.personne || '-';
                equipementText = accident.equipement || '-';
                secteurText = '-'; // Not easily extractable from table
                typeText = accident.type || '-';
                partieBlesseeText = accident.partieBlessée || '-';
                dateText = accident.dateAccident || '-';
            }

            exportData.push([
                dateText,
                personneText,
                accident.typeContrat || '-',
                equipementText,
                secteurText,
                typeText,
                partieBlesseeText,
                accident.causeAccident || '-',
                accident.remarquesAccident || '-',
                accident.joursArrets > 0 ? accident.joursArrets : '-'
            ]);
        });

        console.log(`📊 Export data prepared with ${exportData.length} rows (including header)`);

        if (exportData.length <= 1) {
            Toast.fire({
                icon: 'warning',
                title: 'Aucune donnée à exporter'
            });
            return;
        }

        // Create and download Excel file
        exportToExcel(exportData, 'accidents_export');
    });

    // Gestion de l'ouverture de la modale d'ajout
    document.getElementById('addAccidentBtn').addEventListener('click', function() {
        // Charger les selects simples
        loadSelectData('ajax/get_types.php', 'typeAccident', t => t.typeAccident);

        // Les datalists sont déjà chargées au démarrage
        // Réinitialiser le formulaire
        $('#addAccidentForm')[0].reset();
    });

    // Note: Les gestionnaires pour les champs personnes et équipements
    // sont maintenant gérés dans les modals individuels car ils utilisent
    // une structure dynamique avec plusieurs personnes

    // Soumission formulaire d'ajout (utilisation de la délégation d'événements)
    $(document).on('submit', '#addAccidentForm', function (e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);

        // Debug: Afficher les données du formulaire
        for (let [key, value] of formData.entries()) {
        }

        // Afficher indicateur de chargement dans le bouton
        const submitBtn = $(form).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i> Traitement...').prop('disabled', true);

        $.ajax({
            url: 'Ajax/add_accident.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {

                // Vérifier si la réponse est du JSON valide
                let data;
                try {
                    data = typeof response === 'string' ? JSON.parse(response) : response;
                } catch (e) {
                    console.error('❌ Erreur de parsing JSON:', e);
                    console.error('📄 Réponse brute:', response);
                    throw new Error('Réponse serveur invalide');
                }

                if (data.success) {
                    Toast.fire({
                        icon: 'success',
                        title: 'L\'accident a été ajouté avec succès'
                    });

                    // Réinitialiser le formulaire
                    form.reset();

                    // Fermer le modal
                    document.getElementById('addAccidentModal').classList.add('hidden');

                    // Réinitialiser le conteneur des personnes
                    if (typeof resetPersonnesContainer === 'function') {
                        resetPersonnesContainer();
                    }

                    // Recharger la liste des accidents
                    loadAllAccidentsData();

                } else {
                    console.error('❌ Erreur serveur:', data.error);
                    Toast.fire({
                        icon: 'error',
                        title: data.error || 'Une erreur est survenue lors de l\'ajout'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ Erreur AJAX:', {xhr, status, error});
                console.error('📄 Réponse complète:', xhr.responseText);

                Toast.fire({
                    icon: 'error',
                    title: 'Erreur de connexion: ' + error
                });
            },
            complete: function() {
                // Restaurer le bouton dans tous les cas
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Gestion du clic sur bouton modifier
    $(document).on('click', '.edit-btn', function () {
        const id = $(this).data('id');
        document.getElementById('editAccidentModal').classList.remove('hidden');

        // Afficher indicateur de chargement
        $('#edit-id').closest('form').find('input, select, textarea').prop('disabled', true);

        // Charger les selects simples
        loadSelectData('ajax/get_types.php', 'edit-type', t => t.typeAccident);
        loadSelectData('ajax/get_secteurs.php', 'edit-secteurAccident', s => s.nomSecteur);

        $.getJSON(`ajax/get_accident.php?id=${id}`, function (data) {
            // Remplir les champs du formulaire
            $('#edit-id').val(data.id);
            $('#edit-date').val(data.dateAccident);

            // Gérer le select pour le type (valeur numérique)
            const typeSelect = document.getElementById('edit-type');
            if (typeSelect) {
                // Définir la valeur du type (maintenant c'est un ID numérique)
                typeSelect.value = data.typeAccident;
            }

            // Remplir les champs texte
            $('#edit-cause').val(data.causeAccident);
            $('#edit-remarques').val(data.remarquesAccident);

            // Charger les personnes liées dans le modal d'édition
            if (typeof loadEditPersonnes === 'function') {
                loadEditPersonnes(data.personnes_liées || []);
            }

            // Remplir le secteur accident
            $('#edit-secteurAccident').val(data.secteurAccident || '');

            // Les personnes sont maintenant gérées par le système de personnes multiples
            // dans le modal d'édition via loadEditPersonnes()

            const equipementInput = document.getElementById('edit-equipement');
            const equipementTextInput = document.getElementById('edit-equipement-text');

            if (equipementInput && equipementTextInput && data.idEquipement) {
                // Stocker l'ID dans le champ caché
                equipementInput.value = data.idEquipement;

                // Trouver l'équipement correspondant dans les données préchargées
                const equipement = equipementsData.find(e => e.id == data.idEquipement);
                if (equipement) {
                    // Afficher le nom de l'équipement avec son secteur dans le champ texte
                    equipementTextInput.value = equipement.secteurEquipement
                        ? `${equipement.nomEquipement} - ${equipement.secteurEquipement}`
                        : equipement.nomEquipement;
                }
            }

            // Réactiver les champs
            $('#edit-id').closest('form').find('input, select, textarea').prop('disabled', false);
        }).fail(function() {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors du chargement des données'
            });
            document.getElementById('editAccidentModal').classList.add('hidden');
        });
    });

    // Gestionnaire pour le champ équipement avec datalist (formulaire d'édition)
    const editEquipementTextElement = document.getElementById('edit-equipement-text');
    if (editEquipementTextElement) {
        editEquipementTextElement.addEventListener('input', function() {
            const selectedText = this.value;

            const options = document.querySelectorAll('#equipements-options option');

            let found = false;

            // Chercher l'option correspondante
            for (const option of options) {

                // Vérifier si le texte sélectionné correspond à la valeur de l'option
                // La valeur contient maintenant le nom de l'équipement et son secteur
                if (option.value === selectedText) {
                    const id = option.getAttribute('data-id');

                    // Mettre à jour le champ caché avec l'ID
                    const editEquipementElement = document.getElementById('edit-equipement');
                    if (editEquipementElement) {
                        editEquipementElement.value = id;
                    }

                    // Auto-remplir le secteur de l'accident si pas déjà sélectionné
                    autoFillSecteurFromEquipement(id, 'edit-secteurAccident');

                    found = true;
                    break;
                }
            }

            // Si aucune correspondance, effacer l'ID
            if (!found) {
                const editEquipementElement = document.getElementById('edit-equipement');
                if (editEquipementElement) {
                    editEquipementElement.value = '';
                }
            }
        });

        // Gestionnaire pour la sélection (change)
        editEquipementTextElement.addEventListener('change', function() {
            // Déclencher l'événement input pour traiter la sélection
            this.dispatchEvent(new Event('input'));
        });
    }



    // Soumission formulaire de modification
    $('#editAccidentForm').on('submit', function (e) {
        e.preventDefault();
        const formData = new FormData(this);

        // Afficher indicateur de chargement dans le bouton
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i> Traitement...').prop('disabled', true);

        $.ajax({
            url: 'ajax/update_accident.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success() {
                Toast.fire({
                    icon: 'success',
                    title: 'Les données ont été mises à jour avec succès'
                });
                document.getElementById('editAccidentModal').classList.add('hidden');
                loadAllAccidentsData();
            },
            error() {
                Toast.fire({
                    icon: 'error',
                    title: 'Une erreur est survenue lors de la mise à jour'
                });
            },
            complete() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Gestion suppression avec confirmation
    $(document).on('click', '.delete-btn', function () {
        const id = $(this).data('id');

        Swal.fire({
            title: 'Supprimer cet accident ?',
            text: 'Cette action est irréversible.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            customClass: {
                confirmButton: 'px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 mr-2',
                cancelButton: 'px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
            },
        }).then(result => {
            if (result.isConfirmed) {
                // Afficher indicateur de chargement
                Swal.fire({
                    title: 'Suppression en cours...',
                    text: 'Veuillez patienter',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.post('ajax/delete_accident.php', { id }, function (data) {
                    if (data.success) {
                        Swal.fire({
                            title: 'Supprimé !',
                            text: 'L\'accident a été supprimé avec succès.',
                            icon: 'success',
                            confirmButtonColor: '#0ea5e9'
                        });
                        loadAllAccidentsData();
                    } else {
                        Swal.fire({
                            title: 'Erreur',
                            text: data.error || 'Impossible de supprimer cet accident.',
                            icon: 'error',
                            confirmButtonColor: '#0ea5e9'
                        });
                    }
                }, 'json').fail(function() {
                    Swal.fire({
                        title: 'Erreur',
                        text: 'Une erreur de connexion est survenue.',
                        icon: 'error',
                        confirmButtonColor: '#0ea5e9'
                    });
                });
            }
        });
    });

    // Charger les données pour les filtres
    function initializeFilters() {
        // Attendre que le DOM soit complètement chargé
        setTimeout(() => {
            // Charger les données des filtres simples (selects normaux)
            loadSelectData('ajax/get_types.php', 'filterType', t => t.typeAccident);
            loadSelectData('ajax/get_secteurs.php', 'filterSecteur', s => s.nomSecteur);

            // Les datalists pour personnes et équipements sont déjà chargées au démarrage
            // via la fonction preloadAllData()

            // Initialiser les filtres de date avec l'année courante
            initializeDateFilters();
        }, 100);
    }

    // Fonction pour initialiser les filtres de date avec l'année courante
    function initializeDateFilters() {
        const currentYear = new Date().getFullYear();
        const startDate = `${currentYear}-01-01`;
        const endDate = `${currentYear}-12-31`;

        // Définir les valeurs par défaut seulement si aucune valeur n'est déjà présente
        if (!$('#filterDateDebut').val()) {
            $('#filterDateDebut').val(startDate);
        }
        if (!$('#filterDateFin').val()) {
            $('#filterDateFin').val(endDate);
        }

        console.log('📅 Filtres de date initialisés:', { startDate, endDate });
    }

    // Initialiser les filtres
    initializeFilters();

    // Gestion des filtres avec debounce pour éviter trop de requêtes
    let filterTimeout;

    // Fonction pour appliquer les filtres côté client
    function applyFilters() {
        clearTimeout(filterTimeout);

        filterTimeout = setTimeout(function() {
            // Si les données ne sont pas encore chargées, utiliser l'ancien système
            if (allAccidentsData.length === 0) {
                applyFiltersOldWay();
                return;
            }


            // Récupérer les valeurs des filtres
            const personneText = $('#filterPersonne').val();
            const equipementText = $('#filterEquipement').val();
            let typeFilter = $('#filterType').val();
            let startDateFilter = $('#filterDateDebut').val();
            let endDateFilter = $('#filterDateFin').val();
            const secteurFilter = $('#filterSecteur').val();

            // Récupérer les filtres depuis l'URL si présents (priorité sur les champs)
            const urlParams = new URLSearchParams(window.location.search);
            const urlStartDate = urlParams.get('startDate');
            const urlEndDate = urlParams.get('endDate');
            const urlTypeFilter = urlParams.get('type');

            // Utiliser les valeurs de l'URL si disponibles
            if (urlStartDate) startDateFilter = urlStartDate;
            if (urlEndDate) endDateFilter = urlEndDate;
            if (urlTypeFilter) {
                typeFilter = urlTypeFilter;
                console.log('🔍 Utilisation du type URL:', typeFilter);
            }

            updateFilterCount();

            console.log('🔍 Filtres appliqués:', {
                typeFilter,
                startDateFilter,
                endDateFilter,
                secteurFilter,
                totalAccidents: allAccidentsData.length
            });

            // Filtrer les accidents
            let filteredAccidents = allAccidentsData.filter(accident => {
                // Filtre par personne
                if (personneText) {
                    const personneMatch =
                        (accident.prenomPerso && accident.nomPerso &&
                         `${accident.prenomPerso} ${accident.nomPerso}`.toLowerCase().includes(personneText.toLowerCase())) ||
                        (accident.personneLibre &&
                         accident.personneLibre.toLowerCase().includes(personneText.toLowerCase()));

                    if (!personneMatch) return false;
                }

                // Filtre par équipement
                if (equipementText) {
                    const equipementMatch =
                        (accident.nomEquipement &&
                         accident.nomEquipement.toLowerCase().includes(equipementText.toLowerCase())) ||
                        (accident.secteurEquipement &&
                         equipementText.toLowerCase().includes(accident.secteurEquipement.toLowerCase()));

                    if (!equipementMatch) return false;
                }

                // Filtre par type (gérer les types multiples séparés par des virgules)
                if (typeFilter) {
                    const types = typeFilter.split(',').map(t => parseInt(t.trim()));
                    if (!types.includes(parseInt(accident.typeAccident))) {
                        return false;
                    }
                }

                // Filtre par date de début
                if (startDateFilter) {
                    if (accident.dateAccident < startDateFilter) {
                        return false;
                    }
                }

                // Filtre par date de fin
                if (endDateFilter) {
                    if (accident.dateAccident > endDateFilter) {
                        return false;
                    }
                }

                // Plus de filtre par année - remplacé par les filtres de date

                // Filtre par secteur
                if (secteurFilter) {
                    const secteurMatch =
                        (accident.secteurPersonne === secteurFilter) ||
                        (accident.secteurEquipement === secteurFilter) ||
                        (accident.secteurAccident === secteurFilter);

                    if (!secteurMatch) return false;
                }

                return true;
            });

            console.log('✅ Résultats du filtrage:', {
                totalFiltered: filteredAccidents.length,
                totalOriginal: allAccidentsData.length
            });

            // Afficher les résultats filtrés
            displayFilteredAccidents(filteredAccidents);
        }, 300);
    }

    // Fonction de fallback pour l'ancien système de filtrage
    function applyFiltersOldWay() {
        // Pour les filtres de personne et équipement, on doit chercher l'ID correspondant au texte
        let personneId = '';
        let equipementId = '';

        // Récupérer le texte saisi
        const personneText = $('#filterPersonne').val();
        const equipementText = $('#filterEquipement').val();


        // Chercher l'ID correspondant pour la personne
        if (personneText) {
            const personneOptions = document.querySelectorAll('#personnes-options option');
            for (const option of personneOptions) {
                if (option.value === personneText) {
                    personneId = option.getAttribute('data-id');
                    break;
                }
            }
        }

        // Chercher l'ID correspondant pour l'équipement
        if (equipementText) {
            const equipementOptions = document.querySelectorAll('#equipements-options option');
            for (const option of equipementOptions) {
                if (option.value === equipementText) {
                    equipementId = option.getAttribute('data-id');
                    break;
                }
            }
        }

        // Construire les paramètres avec les IDs pour personne et équipement
        const params = {
            personne: personneId,
            equipement: equipementId,
            type: $('#filterType').val(),
            dateDebut: $('#filterDateDebut').val(),
            dateFin: $('#filterDateFin').val(),
            secteur: $('#filterSecteur').val()
        };


        const query = Object.entries(params)
            .filter(([_, val]) => val)
            .map(([k, v]) => `${k}=${encodeURIComponent(v)}`)
            .join('&');

        loadAccidentsOldWay(query ? '?' + query : '');
    }

    // Écouter les changements sur tous les filtres (selects et inputs)
    $('.filter-select').on('change', applyFilters);

    // Vérifier que les éléments de filtre existent avant d'ajouter les gestionnaires
    const filterPersonneElement = document.getElementById('filterPersonne');
    const filterEquipementElement = document.getElementById('filterEquipement');

    if (filterPersonneElement) {
        $(filterPersonneElement).on('input', applyFilters);
    }

    if (filterEquipementElement) {
        $(filterEquipementElement).on('input', applyFilters);
    }

    // Gestionnaire pour le bouton de réinitialisation
    $('#resetFilters').on('click', function() {
        // Réinitialiser tous les filtres
        $('#filterPersonne').val('');
        $('#filterEquipement').val('');
        $('#filterType').val('');
        $('#filterSecteur').val('');

        // Réinitialiser les dates à l'année courante
        initializeDateFilters();

        // Appliquer les filtres (qui vont maintenant filtrer sur l'année courante)
        applyFilters();

        console.log('🔄 Filtres réinitialisés');
    });

    // Afficher les détails d'un accident
    $(document).on('click', '.view-details-btn', function() {
        const id = $(this).data('id');

        $.getJSON(`ajax/get_accident.php?id=${id}`, function(data) {
            Swal.fire({
                title: 'Détails de l\'accident',
                html: `
                    <div class="text-left">
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Date</p>
                                <p class="text-sm">${new Date(data.dateAccident).toLocaleDateString('fr-FR')}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Type</p>
                                <p class="text-sm">${getTypeLabel(data.typeAccident)}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Personne</p>
                                ${data.prenomPerso && data.nomPerso ?
                                    `<p class="text-sm">${data.prenomPerso} ${data.nomPerso}</p>
                                     ${data.secteurPersonne ? `<p class="text-xs text-gray-500">Secteur: ${data.secteurPersonne}</p>` : ''}
                                     ${data.typeContrat ? `<p class="text-xs text-blue-600">Contrat: ${data.typeContrat}</p>` : ''}` :
                                    data.personneLibre ?
                                        `<p class="text-sm">${data.personneLibre}</p><p class="text-xs text-gray-500">Personne libre</p>` :
                                        '<p class="text-sm">-</p>'
                                }
                                ${data.partieBlessée ? `<p class="text-xs text-red-600">Partie blessée: ${data.partieBlessée}</p>` : ''}
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Équipement</p>
                                <p class="text-sm">${data.nomEquipement || '-'}</p>
                                ${data.secteurEquipement ? `<p class="text-xs text-gray-500">Secteur équipement: ${data.secteurEquipement}</p>` : ''}
                                ${data.secteurAccident && data.secteurAccident !== data.secteurEquipement ? `<p class="text-xs text-blue-600">Secteur accident: ${data.secteurAccident}</p>` : ''}
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Jours d'arrêt</p>
                                <p class="text-sm">${data.joursArrets > 0 ? data.joursArrets : '-'}</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <p class="text-sm font-medium text-gray-500">Cause</p>
                            <p class="text-sm">${data.causeAccident || '-'}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Remarques</p>
                            <p class="text-sm">${data.remarquesAccident || '-'}</p>
                        </div>
                    </div>
                `,
                confirmButtonText: 'Fermer',
                confirmButtonColor: '#0ea5e9',
                customClass: {
                    container: 'swal-wide',
                    popup: 'rounded-lg',
                    confirmButton: 'px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }
            });
        }).fail(function() {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors du chargement des détails'
            });
        });
    });
});

// <span id="filter-count" class="ml-2 text-xs bg-gray-200 text-gray-800 rounded-full px-2 py-1">0</span>
// count the number of filters set
function updateFilterCount() {
    const filterCount = $('.filter-select').filter(function() {
        return $(this).val() !== '';
    }

    ).length + ($('#filterPersonne').val() ? 1 : 0) + ($('#filterEquipement').val() ? 1 : 0);
    $('#filter-count').text(filterCount);
    $('#filter-count').toggle(filterCount > 0);
}


$('#toggleFilters').on('click', function() {
    const container = $('#accident-table-container');

    // Vérifier l'état actuel et basculer entre les deux hauteurs
    if (container.hasClass('max-h-[60vh]')) {
        // Actuellement fermé (petite hauteur) -> ouvrir (grande hauteur)
        container.removeClass('max-h-[60vh] md:max-h-[65vh]').addClass('max-h-[75vh] md:max-h-[80vh]');
    } else {
        // Actuellement ouvert (grande hauteur) -> fermer (petite hauteur)
        container.removeClass('max-h-[75vh] md:max-h-[80vh]').addClass('max-h-[60vh] md:max-h-[75vh]');
    }
});