CREATE TABLE ACCIDENT(
    id int(10) NOT NULL AUTO_INCREMENT,
    typeAccident int(10),
    idEquipement int(10),
    dateAccident date,
    causeAccident VARCHAR(3000),
    remarquesAccident VARCHAR(3000),
    secteurAccident int(10),
    PRIMARY KEY (id)
);

-- Table de liaison pour gérer plusieurs personnes par accident
CREATE TABLE ACCIDENT_PERSONNE(
    id int(10) NOT NULL AUTO_INCREMENT,
    idAccident int(10) NOT NULL,
    idPersonne int(10),
    personneLibre VARCHAR(500),
    typeContrat ENUM('CDD/CDI', 'Client', 'Intérimaire', 'Multiple', 'Prestataire', 'Stagiaire'),
    partieBlessée VARCHAR(500),
    joursArrets int(10),
    PRIMARY KEY (id)
);

ALTER TABLE ACCIDENT    ADD FOREIGN KEY (idEquipement)  REFERENCES EQUIPEMENT(id);
ALTER TABLE ACCIDENT    ADD FOREIGN KEY (secteurAccident)  REFERENCES SECTEUR(id);
ALTER TABLE ACCIDENT_PERSONNE    ADD FOREIGN KEY (idAccident)  REFERENCES ACCIDENT(id) ON DELETE CASCADE;
ALTER TABLE ACCIDENT_PERSONNE    ADD FOREIGN KEY (idPersonne)  REFERENCES PERSONNE(id);
