<?php
require_once 'db.php';

// Configuration
ini_set('memory_limit', '256M');
set_time_limit(300); // 5 minutes

class ExcelImporter {
    private $pdo;
    private $secteurs = [];
    private $equipements = [];
    private $personnes = [];
    private $typeMapping = [
        'situation dangereuse' => 3,
        'premier soin' => 5,
        'premier soins' => 5,
        'presqu\'accident' => 2,
        'presque accident' => 2,
        'accident' => 1,
        'accident de trajet' => 4,
        // Variantes supplémentaires
        'dangereuse' => 3,
        'soin' => 5,
        'soins' => 5,
        'presqu' => 2,
        'presque' => 2,
        'trajet' => 4
    ];

    private $contractMapping = [
        'client' => 'Client',
        'intérimaire' => 'Intérimaire',
        'intrimaire' => 'Intérimaire',
        'interim' => 'Intérimaire',
        'intérim' => 'Intérimaire',
        'multiple' => 'Multiple',
        'prestataire' => 'Prestataire',
        'presta' => 'Prestataire',
        'stagiaire' => 'Stagiaire',
        'stage' => 'Stagiaire',
        'cdd/cdi' => 'CDD/CDI',
    ];

    public function __construct() {
        $this->pdo = getPDO();
        // clear les tables
        $this->pdo->exec("DELETE FROM ACCIDENT_PERSONNE");
        $this->pdo->exec("DELETE FROM ACCIDENT");
        $this->loadReferenceData();
    }

    private function loadReferenceData() {
        // Charger les secteurs
        $stmt = $this->pdo->query("SELECT id, nomSecteur FROM SECTEUR");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->secteurs[strtolower($row['nomSecteur'])] = $row['id'];
        }

        // Charger les équipements
        $stmt = $this->pdo->query("SELECT id, nomEquipement FROM EQUIPEMENT");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->equipements[strtolower($row['nomEquipement'])] = $row['id'];
        }

        // Charger les personnes
        $stmt = $this->pdo->query("SELECT id, prenomPerso, nomPerso FROM PERSONNE");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $fullName = strtolower($row['prenomPerso'] . ' ' . $row['nomPerso']);
            $this->personnes[$fullName] = $row['id'];
        }
    }

    public function importFromCSV($csvFile) {
        $results = [
            'success' => 0,
            'errors' => [],
            'warnings' => []
        ];

        if (!file_exists($csvFile)) {
            throw new Exception("Fichier CSV non trouvé: $csvFile");
        }

        $handle = fopen($csvFile, 'r');
        if (!$handle) {
            throw new Exception("Impossible d'ouvrir le fichier CSV");
        }

        // Lire l'en-tête
        $header = fgetcsv($handle, 0, ';');
        if (!$header) {
            throw new Exception("Impossible de lire l'en-tête du fichier CSV");
        }

        $lineNumber = 1;
        while (($dataRaw = fgetcsv($handle, 0, ';')) !== FALSE) {
            $lineNumber++;

            // 3. Ignorer les lignes totalement vides
            $allEmpty = true;
            foreach ($dataRaw as $cell) {
                if (trim($cell) !== '') {
                    $allEmpty = false;
                    break;
                }
            }
            if ($allEmpty) {
                continue;
            }

            // 4. Convertir chaque champ en UTF-8 (depuis ISO-8859-1 par exemple)
            $data = array_map(function(?string $cell): ?string {
                if ($cell === null) {
                    return null;
                }
                // Ici, le CSV est supposé en ISO-8859-1. Adaptez si nécessaire.
                return mb_convert_encoding($cell, 'UTF-8', 'ISO-8859-1');
            }, $dataRaw);

            // 5. Vérifier qu’il y a au moins 10 colonnes
            if (count($data) < 10) {
                $results['warnings'][] = "Ligne $lineNumber : Données incomplètes, ignorée";
                continue;
            }

            // 6. Appeler importAccident avec les valeurs UTF-8
            try {
                $this->importAccident($data, $lineNumber, $results);
                $results['success']++;
            } catch (Exception $e) {
                $results['errors'][] = "Ligne $lineNumber : " . $e->getMessage();
            }
        }

        fclose($handle);
        print_r("Import terminé. Succès: {$results['success']}, Erreurs: " . count($results['errors']) . ", Avertissements: " . count($results['warnings']) . "\n");
        // print les erreurs et avertissements
        if (!empty($results['errors'])) {
            print_r("<br>");
            print_r("Erreurs:\n" . implode("<br>", $results['errors']) . "<br>");
        }
        if (!empty($results['warnings'])) {
            print_r("<br>");
            print_r("Avertissements:\n" . implode("<br>", $results['warnings']) . "<br>");
        }
        return $results;
    }

    private function importAccident($data, $lineNumber, &$results) {
        // Extraction des données
        $dateStr = trim($data[0]);
        $typeStr = trim($data[1]);
        $secteurStr = trim($data[2]);
        $equipementStr = trim($data[3]);
        $personnesStr = trim($data[4]);
        $contractStr = trim($data[5]);
        $causeStr = trim($data[6]);
        $partieBlesseeStr = trim($data[7]);
        $arretTravailStr = trim($data[8]);
        $joursArretStr = trim($data[9]);

        // Validation et conversion de la date
        $dateAccident = $this->parseDate($dateStr);
        if (!$dateAccident) {
            throw new Exception("Date invalide: $dateStr");
        }

        // Mapping du type d'accident
        $typeAccident = $this->mapType($typeStr);
        if (!$typeAccident) {
            throw new Exception("Type d'accident non reconnu: $typeStr");
        }

        // Recherche du secteur
        $secteurId = $this->findSecteur($secteurStr);
        if (!$secteurId && !empty($secteurStr)) {
            $results['warnings'][] = "Ligne $lineNumber: Secteur '$secteurStr' non trouvé dans la base de données";
        }

        // Recherche de l'équipement
        $equipementId = $this->findEquipement($equipementStr);
        if (!$equipementId && !empty($equipementStr)) {
            $results['warnings'][] = "Ligne $lineNumber: Équipement '$equipementStr' non trouvé dans la base de données";
        }

        // Parsing des personnes
        $personnes = $this->parsePersonnes($personnesStr, $contractStr, $partieBlesseeStr, $joursArretStr, $lineNumber, $results);

        // Commencer une transaction
        $this->pdo->beginTransaction();

        try {
            // Insérer l'accident
            $stmt = $this->pdo->prepare("
                INSERT INTO ACCIDENT
                (typeAccident, idEquipement, dateAccident, causeAccident, secteurAccident)
                VALUES (?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $typeAccident,
                $equipementId,
                $dateAccident,
                $causeStr ?: null,
                $secteurId
            ]);

            $accidentId = $this->pdo->lastInsertId();

            // Insérer les personnes liées
            foreach ($personnes as $personne) {
                $this->insertAccidentPersonne($accidentId, $personne);
            }

            $this->pdo->commit();

        } catch (Exception $e) {
            $this->pdo->rollback();
            throw $e;
        }
    }

    private function parseDate($dateStr) {
        if (empty($dateStr) || $dateStr === '/') {
            return null;
        }

        // Format attendu: dd/mm/yyyy
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $dateStr, $matches)) {
            $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $year = $matches[3];
            return "$year-$month-$day";
        }

        return null;
    }

    private function mapType($typeStr) {
        $typeKey = strtolower(trim($typeStr));

        // Recherche exacte d'abord
        if (isset($this->typeMapping[$typeKey])) {
            return $this->typeMapping[$typeKey];
        }

        // Recherche approximative - chercher des mots-clés dans le type
        foreach ($this->typeMapping as $key => $value) {
            if (strpos($typeKey, $key) !== false || strpos($key, $typeKey) !== false) {
                return $value;
            }
        }

        // Recherche par mots-clés spécifiques
        if (strpos($typeKey, 'danger') !== false) {
            return 3; // Situation dangereuse
        }
        if (strpos($typeKey, 'soin') !== false) {
            return 5; // Premier soins
        }
        if (strpos($typeKey, 'presqu') !== false) {
            return 2; // Presque accident
        }
        if (strpos($typeKey, 'trajet') !== false) {
            return 4; // Accident de trajet
        }
        if (strpos($typeKey, 'accident') !== false && strpos($typeKey, 'presqu') === false && strpos($typeKey, 'trajet') === false) {
            return 1; // Accident simple
        }

        return null;
    }

    private function findSecteur($secteurStr) {
        if (empty($secteurStr)) {
            return null;
        }

        $secteurKey = strtolower(trim($secteurStr));

        // Recherche exacte d'abord
        if (isset($this->secteurs[$secteurKey])) {
            return $this->secteurs[$secteurKey];
        }

        // Recherche approximative (contient)
        foreach ($this->secteurs as $nomSecteur => $id) {
            if (strpos($nomSecteur, $secteurKey) !== false || strpos($secteurKey, $nomSecteur) !== false) {
                return $id;
            }
        }

        return null;
    }

    private function findEquipement($equipementStr) {
        if (empty($equipementStr)) {
            return null;
        }

        $equipementKey = strtolower(trim($equipementStr));

        // Recherche exacte d'abord
        if (isset($this->equipements[$equipementKey])) {
            return $this->equipements[$equipementKey];
        }

        // Recherche approximative (contient)
        foreach ($this->equipements as $nomEquipement => $id) {
            if (strpos($nomEquipement, $equipementKey) !== false || strpos($equipementKey, $nomEquipement) !== false) {
                return $id;
            }
        }

        return null;
    }

    private function parsePersonnes($personnesStr, $contractStr, $partieBlesseeStr, $joursArretStr, $lineNumber = null, &$results = null) {
        $personnes = [];

        if (empty($personnesStr)) {
            return $personnes;
        }

        // Séparer les personnes par différents délimiteurs
        $separators = [',', '&', ' et ', '/'];
        $personnesList = [$personnesStr];

        foreach ($separators as $sep) {
            $newList = [];
            foreach ($personnesList as $item) {
                $newList = array_merge($newList, explode($sep, $item));
            }
            $personnesList = $newList;
        }

        // Nettoyer et traiter chaque personne
        foreach ($personnesList as $personneStr) {
            $personneStr = trim($personneStr);
            if (empty($personneStr)) {
                continue;
            }

            $personne = [
                'idPersonne' => null,
                'personneLibre' => null,
                'typeContrat' => $this->mapContract($contractStr),
                'partieBlessée' => ($partieBlesseeStr && $partieBlesseeStr !== '/') ? $partieBlesseeStr : null,
                'joursArrets' => $this->parseJoursArret($joursArretStr)
            ];

            // Rechercher la personne dans la base
            $personneId = $this->findPersonne($personneStr);
            if ($personneId) {
                $personne['idPersonne'] = $personneId;
                if ($results && $lineNumber) {
                    $results['warnings'][] = "Ligne $lineNumber: Personne '$personneStr' trouvée dans la base de données";
                }
            } else {
                $personne['personneLibre'] = $personneStr;
                if ($results && $lineNumber) {
                    $results['warnings'][] = "Ligne $lineNumber: Personne '$personneStr' ajoutée comme personne libre";
                }
            }

            // Vérifier le type de contrat
            if (!$personne['typeContrat'] && !empty($contractStr)) {
                if ($results && $lineNumber) {
                    $results['warnings'][] = "Ligne $lineNumber: Type de contrat '$contractStr' non reconnu";
                }
            }

            $personnes[] = $personne;
        }

        return $personnes;
    }

    private function findPersonne($personneStr) {
        $personneKey = strtolower(trim($personneStr));

        // Recherche exacte d'abord
        if (isset($this->personnes[$personneKey])) {
            return $this->personnes[$personneKey];
        }

        // Recherche approximative - essayer différentes variantes
        foreach ($this->personnes as $nomComplet => $id) {
            // Vérifier si le nom contient la recherche ou vice versa
            if (strpos($nomComplet, $personneKey) !== false || strpos($personneKey, $nomComplet) !== false) {
                return $id;
            }

            // Essayer de matcher juste le nom de famille (dernier mot)
            $parts = explode(' ', $personneKey);
            $nomFamille = end($parts);
            if (strlen($nomFamille) > 2 && strpos($nomComplet, $nomFamille) !== false) {
                return $id;
            }

            // Essayer de matcher juste le prénom (premier mot)
            $prenom = $parts[0];
            if (strlen($prenom) > 2 && strpos($nomComplet, $prenom) !== false) {
                return $id;
            }
        }

        return null;
    }

    private function mapContract($contractStr) {
        if (empty($contractStr)) {
            return null;
        }

        $contractKey = strtolower(trim($contractStr));

        // Recherche exacte d'abord
        if (isset($this->contractMapping[$contractKey])) {
            return $this->contractMapping[$contractKey];
        }

        // Recherche approximative pour gérer les variantes
        foreach ($this->contractMapping as $key => $value) {
            if (strpos($contractKey, $key) !== false || strpos($key, $contractKey) !== false) {
                return $value;
            }
        }

        return null;
    }

    private function parseJoursArret($joursStr) {
        if (empty($joursStr) || $joursStr === '/') {
            return null;
        }

        $jours = intval($joursStr);
        return $jours > 0 ? $jours : null;
    }

    private function insertAccidentPersonne($accidentId, $personne) {
        $stmt = $this->pdo->prepare("
            INSERT INTO ACCIDENT_PERSONNE
            (idAccident, idPersonne, personneLibre, typeContrat, partieBlessée, joursArrets)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $accidentId,
            $personne['idPersonne'],
            $personne['personneLibre'],
            $personne['typeContrat'],
            $personne['partieBlessée'],
            $personne['joursArrets']
        ]);
    }
}

$importer = new ExcelImporter();
$results = $importer->importFromCSV("data.csv");
$pdo = getPDO();
$pdo->exec("UPDATE ACCIDENT SET causeAccident = REPLACE(causeAccident, '', \"'\") WHERE causeAccident LIKE '%%'");

echo json_encode([
    'success' => true,
    'results' => $results
]);

exit;
?>
