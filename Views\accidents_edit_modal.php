<!-- Modal d'édition d'accident -->
<div class="fixed inset-0 z-50 hidden overflow-y-auto" id="editAccidentModal" aria-labelledby="editAccidentModalLabel" aria-hidden="true">
  <div class="flex items-center justify-center min-h-screen p-4 text-center sm:p-0">
    <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>

    <!-- Modal content -->
    <div class="relative inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white rounded-lg shadow-xl">
      <form id="editAccidentForm" class="space-y-4">
        <input type="hidden" name="id" id="edit-id">

        <div class="flex justify-between items-center pb-3 border-b border-gray-200">
          <h3 class="text-lg font-medium leading-6 text-gray-900" id="editAccidentModalLabel">
            <i class="fas fa-edit text-primary-600 mr-2"></i>Modifier un accident
          </h3>
          <button type="button" class="text-gray-400 hover:text-gray-500" data-bs-dismiss="modal" aria-label="Fermer">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Main content in two columns -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Left Column -->
          <div class="space-y-6">
            <!-- Informations générales de l'accident -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                Informations générales
              </h4>
              <div class="space-y-4">
                <!-- Date -->
                <div>
                  <label for="edit-date" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
                  <input type="date" name="dateAccident" id="edit-date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
                </div>

                <!-- Type -->
                <div>
                  <label for="edit-type" class="block text-sm font-medium text-gray-700 mb-1">Type d'événement *</label>
                  <select name="typeAccident" id="edit-type" class="custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
                    <option value="">Sélectionner...</option>
                    <option value="1">🔴 Accident</option>
                    <option value="4">🔴 Accident de trajet</option>
                    <option value="5">🔵 Premier soins</option>
                    <option value="2">🟡 Presque accident</option>
                    <option value="3">🟢 Situation dangereuse</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Équipement et lieu -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-cogs text-blue-500 mr-2"></i>
                Équipement et lieu
              </h4>
              <div class="space-y-4">
                <!-- Équipement -->
                <div>
                  <label for="edit-equipement-text" class="block text-sm font-medium text-gray-700 mb-1">Équipement concerné</label>
                  <input
                    type="text"
                    id="edit-equipement-text"
                    list="equipements-options"
                    placeholder="Rechercher un équipement"
                    class="custom-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    autocomplete="off"
                  >
                  <input type="hidden" name="idEquipement" id="edit-equipement">
                </div>

                <!-- Secteur de l'accident -->
                <div>
                  <label for="edit-secteurAccident" class="block text-sm font-medium text-gray-700 mb-1">Secteur de l'accident</label>
                  <select name="secteurAccident" id="edit-secteurAccident" class="custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    <option value="">Sélectionner...</option>
                  </select>
                  <p class="text-xs text-gray-500 mt-1">
                    <i class="fas fa-info-circle mr-1"></i>
                    Se remplit automatiquement selon l'équipement
                  </p>
                </div>
              </div>
            </div>

            
          </div>

          <!-- Right Column -->
          <div class="space-y-6">
            <!-- Personnes impliquées -->
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">
                  <i class="fas fa-users text-green-500 mr-2"></i>
                  Personnes impliquées
                </h4>
                <button type="button" id="edit-addPersonneBtn" class="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                  <i class="fas fa-plus mr-1"></i> Ajouter une personne
                </button>
              </div>

              <div id="edit-personnesContainer">
                <!-- Les personnes seront ajoutées ici dynamiquement -->
              </div>

              <p class="text-xs text-gray-500 mt-2">
                <i class="fas fa-info-circle mr-1"></i>
                Vous pouvez ajouter plusieurs personnes impliquées dans l'accident. Laissez vide si aucune personne n'est concernée.
              </p>
            </div>

          </div>
        </div>
        <!-- Cause et Remarques -->
            <div class="bg-yellow-50 p-4 rounded-lg">
              <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-clipboard-list text-yellow-500 mr-2"></i>
                Détails supplémentaires
              </h4>
              <div class="space-y-4">
                <!-- Cause -->
                <div>
                  <label for="edit-cause" class="block text-sm font-medium text-gray-700 mb-1">Cause</label>
                  <textarea name="causeAccident" id="edit-cause" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>

                <!-- Remarques -->
                <div>
                  <label for="edit-remarques" class="block text-sm font-medium text-gray-700 mb-1">Remarques</label>
                  <textarea name="remarquesAccident" id="edit-remarques" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
              </div>
            </div>

        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" data-bs-dismiss="modal">
            Annuler
          </button>
          <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-save mr-1"></i> Enregistrer
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  // Variables pour gérer les personnes en édition
  let editPersonneCounter = 0;

  // Initialize modal
  document.addEventListener('DOMContentLoaded', function() {
    // Hide modal when close button is clicked
    document.querySelectorAll('#editAccidentModal [data-bs-dismiss="modal"]').forEach(button => {
      button.addEventListener('click', function() {
        const modal = document.getElementById('editAccidentModal');
        modal.classList.add('hidden');
      });
    });

    // Gestionnaire pour ajouter une personne en édition
    document.getElementById('edit-addPersonneBtn').addEventListener('click', function() {
      addEditPersonneRow();
    });
  });

  // Fonction pour charger les personnes existantes dans le modal d'édition
  function loadEditPersonnes(personnesLiées) {
    const container = document.getElementById('edit-personnesContainer');
    container.innerHTML = '';
    editPersonneCounter = 0;

    if (personnesLiées && personnesLiées.length > 0) {
      personnesLiées.forEach(personne => {
        addEditPersonneRow(personne);
      });
    } else {
      // Ajouter une personne vide par défaut
      addEditPersonneRow();
    }
  }

  // Fonction pour ajouter une ligne de personne en édition
  function addEditPersonneRow(personneData = null) {
    const container = document.getElementById('edit-personnesContainer');
    const personneId = editPersonneCounter++;

    const personneRow = document.createElement('div');
    personneRow.className = 'edit-personne-row bg-white p-4 border border-gray-200 rounded-lg mb-3';
    personneRow.setAttribute('data-personne-id', personneId);

    // Déterminer le type de personne et les valeurs
    const isPersonneLibre = personneData && personneData.personneLibre && !personneData.idPersonne;
    const personneText = personneData && personneData.idPersonne ?
      `${personneData.prenomPerso} ${personneData.nomPerso}` : '';

    personneRow.innerHTML = `
      <div class="flex justify-between items-start mb-3">
        <h5 class="text-sm font-medium text-gray-700">
          <i class="fas fa-user mr-1"></i>
          Personne ${personneId + 1}
        </h5>
        ${editPersonneCounter > 1 ? `
          <button type="button" class="edit-remove-personne-btn text-red-500 hover:text-red-700 focus:outline-none" data-personne-id="${personneId}">
            <i class="fas fa-trash-alt"></i>
          </button>
        ` : ''}
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Choix du type de personne -->
        <div class="md:col-span-2">
          <div class="flex space-x-4">
            <label class="flex items-center">
              <input type="radio" name="editPersonneType_${personneId}" value="existante" class="mr-2" ${!isPersonneLibre ? 'checked' : ''} onchange="toggleEditPersonneInputs(${personneId})">
              <span class="text-sm">Personne existante</span>
            </label>
            <label class="flex items-center">
              <input type="radio" name="editPersonneType_${personneId}" value="libre" class="mr-2" ${isPersonneLibre ? 'checked' : ''} onchange="toggleEditPersonneInputs(${personneId})">
              <span class="text-sm">Personne libre</span>
            </label>
          </div>
        </div>

        <!-- Personne existante -->
        <div class="edit-personne-existante-${personneId}" ${isPersonneLibre ? 'style="display: none;"' : ''}>
          <label class="block text-sm font-medium text-gray-700 mb-1">Personne</label>
          <input
            type="text"
            id="editPersonneText_${personneId}"
            list="personnes-options"
            placeholder="Rechercher une personne"
            value="${personneText}"
            class="custom-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            autocomplete="off"
            onchange="updateEditPersonneId(${personneId})"
          >
          <input type="hidden" name="personnes[${personneId}][idPersonne]" id="editIdPersonne_${personneId}" value="${personneData?.idPersonne || ''}">
        </div>

        <!-- Personne libre -->
        <div class="edit-personne-libre-${personneId}" ${!isPersonneLibre ? 'style="display: none;"' : ''}>
          <label class="block text-sm font-medium text-gray-700 mb-1">Nom de la personne</label>
          <input
            type="text"
            name="personnes[${personneId}][personneLibre]"
            placeholder="Nom de la personne en texte libre"
            value="${personneData?.personneLibre || ''}"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
        </div>

        <!-- Type de contrat -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Type de contrat</label>
          <select name="personnes[${personneId}][typeContrat]" class="custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
            <option value="">Sélectionner...</option>
            <option value="CDD/CDI" ${personneData?.typeContrat === 'CDD/CDI' ? 'selected' : ''}>CDD/CDI</option>
            <option value="Client" ${personneData?.typeContrat === 'Client' ? 'selected' : ''}>Client</option>
            <option value="Intérimaire" ${personneData?.typeContrat === 'Intérimaire' ? 'selected' : ''}>Intérimaire</option>
            <option value="Multiple" ${personneData?.typeContrat === 'Multiple' ? 'selected' : ''}>Multiple</option>
            <option value="Prestataire" ${personneData?.typeContrat === 'Prestataire' ? 'selected' : ''}>Prestataire</option>
            <option value="Stagiaire" ${personneData?.typeContrat === 'Stagiaire' ? 'selected' : ''}>Stagiaire</option>
          </select>
        </div>

        <!-- Jours d'arrêt -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Jours d'arrêt</label>
          <input
            type="number"
            name="personnes[${personneId}][joursArrets]"
            min="0"
            placeholder="0"
            value="${personneData?.joursArrets || ''}"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
        </div>

        <!-- Partie blessée -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">Partie blessée (optionnel)</label>
          <input
            type="text"
            name="personnes[${personneId}][partieBlessée]"
            placeholder="Ex: Main droite, Genou gauche..."
            value="${personneData?.partieBlessée || ''}"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
        </div>
      </div>
    `;

    container.appendChild(personneRow);

    // Ajouter les gestionnaires d'événements pour la suppression
    const removeBtn = personneRow.querySelector('.edit-remove-personne-btn');
    if (removeBtn) {
      removeBtn.addEventListener('click', function() {
        removeEditPersonneRow(personneId);
      });
    }
  }

  // Fonction pour basculer entre personne existante et libre en édition
  function toggleEditPersonneInputs(personneId) {
    const existanteDiv = document.querySelector(`.edit-personne-existante-${personneId}`);
    const libreDiv = document.querySelector(`.edit-personne-libre-${personneId}`);
    const radioExistante = document.querySelector(`input[name="editPersonneType_${personneId}"][value="existante"]`);

    if (radioExistante.checked) {
      existanteDiv.style.display = 'block';
      libreDiv.style.display = 'none';
    } else {
      existanteDiv.style.display = 'none';
      libreDiv.style.display = 'block';
    }
  }

  // Fonction pour mettre à jour l'ID de la personne sélectionnée en édition
  function updateEditPersonneId(personneId) {
    const input = document.getElementById(`editPersonneText_${personneId}`);
    const hiddenInput = document.getElementById(`editIdPersonne_${personneId}`);
    const selectedText = input.value;

    // Chercher l'ID correspondant dans les options
    const options = document.querySelectorAll('#personnes-options option');
    let found = false;

    for (const option of options) {
      if (option.value === selectedText) {
        const id = option.getAttribute('data-id');
        hiddenInput.value = id;
        found = true;
        break;
      }
    }

    if (!found) {
      hiddenInput.value = '';
    }
  }

  // Fonction pour supprimer une ligne de personne en édition
  function removeEditPersonneRow(personneId) {
    const row = document.querySelector(`[data-personne-id="${personneId}"]`);
    if (row) {
      row.remove();
    }

    // Renommer les personnes restantes
    renumberEditPersonnes();
  }

  // Fonction pour renuméroter les personnes après suppression en édition
  function renumberEditPersonnes() {
    const rows = document.querySelectorAll('.edit-personne-row');
    rows.forEach((row, index) => {
      const title = row.querySelector('h5');
      if (title) {
        title.innerHTML = `<i class="fas fa-user mr-1"></i>Personne ${index + 1}`;
      }
    });
  }
</script>
