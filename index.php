<?php
require_once 'db.php';

// Récupérer les paramètres de filtrage depuis l'URL
$urlFilters = [
    'startDate' => $_GET['startDate'] ?? '',
    'endDate' => $_GET['endDate'] ?? '',
    'sector' => $_GET['sector'] ?? '',
    'type' => $_GET['type'] ?? ''
];

ob_start();
?>
<div x-data="{ showFilters: false }">
  <!-- Header with title and actions -->
  <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
    <div class="flex items-center space-x-2">
      <h1 class="text-2xl font-bold text-gray-800">Evènements</h1>
      <span class="bg-primary-100 text-primary-800 font-medium px-2.5 py-0.5 rounded-full" id="accident-count">0</span>
    </div>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-2 md:mt-0 w-full md:w-auto">
      <button
        @click="showFilters = !showFilters"
        class="flex items-center justify-center px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        id="toggleFilters"
      >
        <i class="fas fa-filter mr-2"></i>
        <span x-text="showFilters ? 'Masquer les filtres' : 'Afficher les filtres'">Filtres</span>
        <span id="filter-count" class="ml-2 text-xs bg-gray-200 text-gray-800 rounded-full px-2 py-1">0</span>
      </button>
      <a
        href="analytics.php"
        class="flex items-center justify-center px-4 py-2 bg-purple-600 rounded-lg shadow-sm text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
      >
        <i class="fas fa-chart-bar mr-2"></i>
        Tableau de bord
      </a>
      <button
        id="exportExcelBtn"
        class="flex items-center justify-center px-4 py-2 bg-green-600 rounded-lg shadow-sm text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
      >
        <i class="fas fa-file-excel mr-2"></i>
        Exporter Excel
      </button>
      <button
        id="addAccidentBtn"
        class="flex items-center justify-center px-4 py-2 bg-primary-600 rounded-lg shadow-sm text-sm font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
      >
        <i class="fas fa-plus mr-2"></i>
        Ajouter un accident
      </button>
    </div>
  </div>

  <!-- Filters section -->
  <div
    x-show="showFilters"
    x-transition:enter="transition ease-out duration-200"
    x-transition:enter-start="opacity-0 transform -translate-y-2"
    x-transition:enter-end="opacity-100 transform translate-y-0"
    x-transition:leave="transition ease-in duration-150"
    x-transition:leave-start="opacity-100 transform translate-y-0"
    x-transition:leave-end="opacity-0 transform -translate-y-2"
    class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200"
  >
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <div>
        <label for="filterPersonne" class="block text-sm font-medium text-gray-700 mb-1">Personne</label>
        <input
          type="text"
          id="filterPersonne"
          list="personnes-options"
          placeholder="Toutes les personnes"
          class="custom-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        >
      </div>
      <div>
        <label for="filterEquipement" class="block text-sm font-medium text-gray-700 mb-1">Équipement</label>
        <input
          type="text"
          id="filterEquipement"
          list="equipements-options"
          placeholder="Tous les équipements"
          class="custom-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        >
      </div>
      <div>
        <label for="filterType" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
        <select id="filterType" class="filter-select custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
          <option value="">Tous les types</option>
        </select>
      </div>
      <div>
        <label for="filterSecteur" class="block text-sm font-medium text-gray-700 mb-1">Secteur</label>
        <select id="filterSecteur" class="filter-select custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
          <option value="">Tous les secteurs</option>
        </select>
      </div>
      <div>
        <label for="filterAnnee" class="block text-sm font-medium text-gray-700 mb-1">Année</label>
        <select id="filterAnnee" class="filter-select custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
          <option value="">Toutes les années</option>
          <option value="2025">2025</option>
        </select>
      </div>
    </div>
    <div class="flex justify-end mt-4">
      <button id="resetFilters" class="text-sm text-gray-600 hover:text-gray-900 flex items-center">
        <i class="fas fa-undo mr-1"></i> Réinitialiser les filtres
      </button>
    </div>
  </div>

  <!-- Loading indicator -->
  <div id="loading-indicator" class="hidden flex justify-center my-8">
    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-600"></div>
  </div>

  <!-- Accidents container -->
  <div id="accident-container" class="min-h-[200px]"></div>

  <!-- Datalists pour les options -->
  <datalist id="personnes-options"></datalist>
  <datalist id="equipements-options"></datalist>
</div>

<script src="js/main.js"></script>

<script>
// Appliquer les filtres venant de l'URL (depuis le tableau de bord)
document.addEventListener('DOMContentLoaded', function() {
    const urlFilters = <?= json_encode($urlFilters) ?>;

    // Si des filtres sont présents dans l'URL, les appliquer
    if (urlFilters.startDate || urlFilters.endDate || urlFilters.sector || urlFilters.type) {
        // Afficher les filtres
        const showFiltersBtn = document.querySelector('[\\@click="showFilters = !showFilters"]');
        if (showFiltersBtn && !document.querySelector('[x-data]').__x.$data.showFilters) {
            showFiltersBtn.click();
        }

        // Attendre que les filtres soient chargés puis les appliquer
        setTimeout(() => {
            console.log('🔍 Application des filtres URL:', urlFilters);

            // Appliquer les filtres de type
            if (urlFilters.type) {
                const typeFilter = document.getElementById('filterType');
                if (typeFilter) {
                    // Stocker le type dans un attribut data pour le filtrage
                    typeFilter.setAttribute('data-url-type', urlFilters.type);

                    // Pour l'affichage, sélectionner le premier type trouvé
                    if (urlFilters.type.includes('1')) {
                        typeFilter.value = '1'; // Accident
                    } else if (urlFilters.type.includes('4')) {
                        typeFilter.value = '4'; // Accident de trajet
                    } else if (urlFilters.type.includes('5')) {
                        typeFilter.value = '5'; // Premier soins
                    } else if (urlFilters.type.includes('2')) {
                        typeFilter.value = '2'; // Presque accident
                    } else if (urlFilters.type.includes('3')) {
                        typeFilter.value = '3'; // Situation dangereuse
                    }

                    console.log('✅ Type filter appliqué:', typeFilter.value, 'URL type:', urlFilters.type);
                }
            }

            // Appliquer le filtre secteur
            if (urlFilters.sector) {
                const sectorFilter = document.getElementById('filterSecteur');
                if (sectorFilter) {
                    sectorFilter.value = urlFilters.sector;
                    console.log('✅ Secteur filter appliqué:', urlFilters.sector);
                }
            }

            // Déclencher le filtrage
            console.log('🚀 Déclenchement du filtrage...');
            if (typeof applyFilters === 'function') {
                applyFilters();
            } else if (typeof loadAllAccidentsData === 'function') {
                loadAllAccidentsData();
            }
        }, 1500); // Augmenter le délai pour s'assurer que tout est chargé
    }
});
</script>

<?php
$content = ob_get_clean();
$title = "Gestion des Accidents";
require 'template.php';
require 'Views/accidents_form_modal.php';
require 'Views/accidents_edit_modal.php';
