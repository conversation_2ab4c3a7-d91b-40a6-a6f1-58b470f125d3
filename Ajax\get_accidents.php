<?php
require_once '../db.php';
$pdo = getPDO();

$where = [];
$params = [];

if (!empty($_GET['personne'])) {
  $where[] = 'A.idPersonne = ?';
  $params[] = $_GET['personne'];
}
if (!empty($_GET['equipement'])) {
  $where[] = 'A.idEquipement = ?';
  $params[] = $_GET['equipement'];
}
if (!empty($_GET['type'])) {
  $where[] = 'A.typeAccident = ?';
  $params[] = $_GET['type'];
}
if (!empty($_GET['annee'])) {
  $where[] = 'YEAR(A.dateAccident) = ?';
  $params[] = $_GET['annee'];
}
if (!empty($_GET['secteur'])) {
    $where[] = '(P.idSecteur = ? OR E.idSecteur = ?)';
    $params[] = $_GET['secteur'];
    $params[] = $_GET['secteur'];
}


$whereSql = $where ? 'WHERE ' . implode(' AND ', $where) : '';

$stmt = $pdo->prepare("
  SELECT
    A.id, A.dateAccident, A.typeAccident, A.causeAccident,
    A.remarquesAccident,
    E.nomEquipement,
    S2.nomSecteur as secteurEquipement,
    S3.nomSecteur as secteurAccident,
    AP.personneLibre, AP.typeContrat, AP.partieBlessée, AP.joursArrets,
    P.prenomPerso, P.nomPerso,
    S1.nomSecteur as secteurPersonne,
    COALESCE(PC.nombrePersonnes, 0) as nombrePersonnes
  FROM accident A
  LEFT JOIN equipement E ON E.id = A.idEquipement
  LEFT JOIN secteur S2 ON S2.id = E.idSecteur
  LEFT JOIN secteur S3 ON S3.id = A.secteurAccident
  LEFT JOIN (
    SELECT DISTINCT
      idAccident,
      FIRST_VALUE(idPersonne) OVER (PARTITION BY idAccident ORDER BY id) as idPersonne,
      FIRST_VALUE(personneLibre) OVER (PARTITION BY idAccident ORDER BY id) as personneLibre,
      FIRST_VALUE(typeContrat) OVER (PARTITION BY idAccident ORDER BY id) as typeContrat,
      FIRST_VALUE(partieBlessée) OVER (PARTITION BY idAccident ORDER BY id) as partieBlessée,
      FIRST_VALUE(joursArrets) OVER (PARTITION BY idAccident ORDER BY id) as joursArrets
    FROM accident_personne
  ) AP ON AP.idAccident = A.id
  LEFT JOIN personne P ON P.id = AP.idPersonne
  LEFT JOIN secteur S1 ON S1.id = P.idSecteur
  LEFT JOIN (
    SELECT idAccident, COUNT(*) as nombrePersonnes
    FROM accident_personne
    GROUP BY idAccident
  ) PC ON PC.idAccident = A.id
  $whereSql
  ORDER BY A.dateAccident DESC
");

$stmt->execute($params);
$accidents = $stmt->fetchAll(PDO::FETCH_ASSOC);

include '../views/accidents.php';

